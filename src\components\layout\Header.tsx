"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { 
  Calculator, 
  BookOpen, 
  HelpCircle, 
  Menu, 
  X,
  Globe
} from "lucide-react";
import LanguageSwitcher from "../LanguageSwitcher";
import { cn } from "@/lib/utils";

interface HeaderProps {
  dict: any;
  lang: 'en' | 'fr' | 'ar';
}

export function Header({ dict, lang }: HeaderProps) {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  // Extract language from pathname as fallback
  const currentLang = lang || (pathname?.split('/')[1] as 'en' | 'fr' | 'ar') || 'en';

  const navigation = [
    {
      name: dict.navigation?.home || 'Calculator',
      href: `/${currentLang}`,
      icon: Calculator,
      description: dict.navigation?.homeDescription || 'Free loan calculator for Morocco'
    },
    {
      name: dict.navigation?.blog || 'Blog',
      href: `/${currentLang}/blog`,
      icon: BookOpen,
      description: dict.navigation?.blogDescription || 'Financial insights and tips'
    },
    {
      name: dict.navigation?.faq || 'FAQ',
      href: `/${currentLang}/faq`,
      icon: HelpCircle,
      description: dict.navigation?.faqDescription || 'Common questions answered'
    }
  ];

  const isActive = (href: string) => {
    if (href === `/${currentLang}`) {
      return pathname === `/${currentLang}` || pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link
              href={`/${currentLang}`}
              className="flex items-center space-x-2 hover:opacity-80 transition-opacity cursor-pointer"
            >
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <Calculator className="h-5 w-5" />
              </div>
              <span className="text-xl font-bold text-primary">{dict.header?.title || 'RibaCalc'}</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-2 text-sm font-medium transition-colors hover:text-primary",
                    isActive(item.href)
                      ? "text-primary"
                      : "text-muted-foreground"
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <LanguageSwitcher />
          </div>

          {/* Mobile Menu */}
          <div className="md:hidden flex items-center space-x-2">
            <LanguageSwitcher />
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="px-2">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <div className="flex items-center justify-between mb-6">
                  <Link
                    href={`/${currentLang}`}
                    className="flex items-center space-x-2 hover:opacity-80 transition-opacity cursor-pointer"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                      <Calculator className="h-5 w-5" />
                    </div>
                    <span className="text-xl font-bold text-primary">{dict.header?.title || 'RibaCalc'}</span>
                  </Link>
                </div>
                
                <nav className="space-y-4">
                  {navigation.map((item) => {
                    const Icon = item.icon;
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        onClick={() => setIsOpen(false)}
                        className={cn(
                          "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
                          isActive(item.href)
                            ? "bg-accent text-accent-foreground"
                            : "text-muted-foreground"
                        )}
                      >
                        <Icon className="h-5 w-5" />
                        <div className="flex flex-col">
                          <span>{item.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {item.description}
                          </span>
                        </div>
                      </Link>
                    );
                  })}
                </nav>

                <div className="mt-8 pt-6 border-t">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Globe className="h-4 w-4" />
                    <span>{dict.navigation?.language || 'Language'}</span>
                  </div>
                  <div className="mt-2">
                    <LanguageSwitcher />
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}
