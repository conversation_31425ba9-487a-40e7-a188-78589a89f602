# Sitemap Documentation

## Overview
This document describes the comprehensive sitemap structure implemented for the RibaCalc website to ensure optimal SEO and search engine indexing.

## Sitemap Structure

### 1. Main Sitemap (`/sitemap.xml`)
The primary sitemap that includes all pages with dynamic priority calculation based on:
- Page type (calculator pages have highest priority)
- Content freshness (recent blog posts get priority boost)
- Featured content status

### 2. Sitemap Index (`/sitemap-index.xml`)
A sitemap index that organizes all sitemaps for better management:
- Main sitemap
- Pages sitemap
- Blog sitemap
- Images sitemap

### 3. Pages Sitemap (`/sitemap-pages.xml`)
Contains all static pages with multilingual support:
- Root page (`/`)
- Calculator pages (`/en`, `/fr`, `/ar`)
- FAQ pages (`/en/faq`, `/fr/faq`, `/ar/faq`)
- Blog listing pages (`/en/blog`, `/fr/blog`, `/ar/blog`)

### 4. Blog Sitemap (`/sitemap-blog.xml`)
Dynamic sitemap for all blog posts with:
- Language-specific URLs
- Dynamic priority based on recency and featured status
- Proper hreflang annotations for multilingual content

### 5. Images Sitemap (`/sitemap-images.xml`)
Contains image metadata for better image SEO:
- Calculator preview images
- Multilingual captions and titles

## Features

### Dynamic Domain Configuration
All sitemaps use environment variables for flexible deployment:
```typescript
const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.VERCEL_URL 
  ? `https://${process.env.VERCEL_URL}` 
  : 'http://localhost:3000'
```

### Multilingual Support
- Proper hreflang annotations for all languages (en, fr, ar)
- Language-specific URLs and content
- Cross-language linking for better SEO

### Priority Calculation
Blog posts use dynamic priority calculation:
- Base priority: 0.6
- Featured posts: +0.2
- Recent posts (< 30 days): +0.1
- Very recent posts (< 7 days): +0.1
- Maximum priority: 0.9

### Cache Control
Different cache strategies for different content types:
- Static pages: 1 hour cache
- Blog posts: 30 minutes cache
- Images: 24 hours cache

## Environment Variables

Set these environment variables for production:

```env
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
```

For Vercel deployment, the `VERCEL_URL` is automatically provided.

## SEO Benefits

1. **Complete Coverage**: All pages are indexed
2. **Priority Guidance**: Search engines understand page importance
3. **Freshness Signals**: Recent content is prioritized
4. **Multilingual SEO**: Proper language targeting
5. **Image SEO**: Enhanced image discoverability
6. **Organized Structure**: Better crawl efficiency

## Robots.txt Integration

The robots.txt file automatically references the main sitemap:
```
Sitemap: https://yourdomain.com/sitemap.xml
```

## Testing

All sitemaps can be tested locally:

```bash
# Start development server
npm run dev

# Test sitemaps
curl http://localhost:3000/sitemap.xml
curl http://localhost:3000/sitemap-index.xml
curl http://localhost:3000/sitemap-pages.xml
curl http://localhost:3000/sitemap-blog.xml
curl http://localhost:3000/sitemap-images.xml
curl http://localhost:3000/robots.txt
```

## Validation

Validate sitemaps using:
- [Google Search Console Sitemap Tester](https://search.google.com/search-console)
- [XML Sitemap Validator](https://www.xml-sitemaps.com/validate-xml-sitemap.html)
- [Sitemap Checker Tools](https://sitemap-checker.com/)

## Monitoring

Monitor sitemap performance through:
- Google Search Console
- Bing Webmaster Tools
- Regular sitemap validation tools

## Maintenance

Sitemaps are automatically updated when:
- New blog posts are added
- Content is modified
- Pages are added or removed

The dynamic nature ensures sitemaps stay current without manual intervention.

## Troubleshooting

### Common Issues

1. **404 on sitemap.xml**: Check middleware configuration excludes sitemap files
2. **Wrong domain in URLs**: Verify environment variables are set correctly
3. **Missing pages**: Ensure all routes are included in static pages array
4. **Blog posts not appearing**: Check blog data source and slug generation

### Middleware Configuration

The middleware must exclude sitemap files:

```typescript
// Skip middleware for sitemap files, robots.txt, and other SEO files
if (pathname.startsWith('/sitemap') || pathname === '/robots.txt' || pathname.endsWith('.xml')) {
  return NextResponse.next()
}
```
