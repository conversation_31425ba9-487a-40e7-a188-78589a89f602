import { getDictionary } from '@/lib/dictionaries'
import { BlogPost } from '@/components/blog/BlogPost'
import { getBlogPost, getBlogPosts, BlogPost as BlogPostType } from '@/lib/blog'
import { notFound } from 'next/navigation'

type PageProps = {
    params: Promise<{ lang: 'en' | 'fr' | 'ar'; slug: string }>;
};

export default async function BlogPostPage({ params }: PageProps) {
  const { lang, slug } = await params;
  const dict = await getDictionary(lang);
  const post: BlogPostType | null = await getBlogPost(slug, lang);

  if (!post) {
    notFound();
  }
  
  return <BlogPost dict={dict} lang={lang} post={post} />
}

export async function generateMetadata({ params }: PageProps) {
  const { lang, slug } = await params;
  const dict = await getDictionary(lang);
  const post: BlogPostType | null = await getBlogPost(slug, lang);

  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }
  
  return {
    title: `${post.title} | ${dict.seo.title}`,
    description: post.excerpt,
    alternates: {
      canonical: `/${lang}/blog/${slug}`,
      languages: {
        'en': `/en/blog/${slug}`,
        'fr': `/fr/blog/${slug}`,
        'ar': `/ar/blog/${slug}`,
      },
    },
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: 'article',
      publishedTime: post.publishedAt,
      authors: [post.author],
    },
  };
}

export async function generateStaticParams() {
  const enPosts: BlogPostType[] = await getBlogPosts('en');
  const frPosts: BlogPostType[] = await getBlogPosts('fr');

  return [
    ...enPosts.map((post: BlogPostType) => ({ lang: 'en' as const, slug: post.slug })),
    ...frPosts.map((post: BlogPostType) => ({ lang: 'fr' as const, slug: post.slug })),
  ];
}
