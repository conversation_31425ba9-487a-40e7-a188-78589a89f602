import 'server-only'

type Locale = 'en' | 'fr' | 'ar';

const dictionaries = {
  en: () => import('@/dictionaries/en.json').then((module) => module.default),
  fr: () => import('@/dictionaries/fr.json').then((module) => module.default),
  ar: () => import('@/dictionaries/ar.json').then((module) => module.default),
}

export const getDictionary = async (locale: Locale) => {
    if (locale in dictionaries) {
        return dictionaries[locale]();
    }
    return dictionaries.en();
}
