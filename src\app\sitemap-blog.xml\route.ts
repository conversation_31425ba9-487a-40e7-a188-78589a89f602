import { NextResponse } from 'next/server'
import { getBlogPosts, BlogPost } from '@/lib/blog'
import { getBaseUrl } from '@/lib/url'

export async function GET() {
  const baseUrl = getBaseUrl()

  const currentDate = new Date()

  // Get all blog posts for all languages
  const enPosts = await getBlogPosts('en')
  const frPosts = await getBlogPosts('fr')
  const arPosts = await getBlogPosts('ar')

  // Generate blog post URLs with enhanced metadata
  const blogUrls = [
    ...enPosts.map((post: BlogPost) => {
      const publishDate = new Date(post.publishedAt)
      const daysSincePublished = Math.floor((currentDate.getTime() - publishDate.getTime()) / (1000 * 60 * 60 * 24))
      
      let priority = 0.6
      if (post.featured) priority += 0.2
      if (daysSincePublished < 30) priority += 0.1
      if (daysSincePublished < 7) priority += 0.1

      return {
        url: `${baseUrl}/en/blog/${post.slug}`,
        lastmod: publishDate.toISOString(),
        changefreq: daysSincePublished < 30 ? 'weekly' : 'monthly',
        priority: Math.min(priority, 0.9).toFixed(1),
        lang: 'en',
        slug: post.slug
      }
    }),
    ...frPosts.map((post: BlogPost) => {
      const publishDate = new Date(post.publishedAt)
      const daysSincePublished = Math.floor((currentDate.getTime() - publishDate.getTime()) / (1000 * 60 * 60 * 24))
      
      let priority = 0.6
      if (post.featured) priority += 0.2
      if (daysSincePublished < 30) priority += 0.1
      if (daysSincePublished < 7) priority += 0.1

      return {
        url: `${baseUrl}/fr/blog/${post.slug}`,
        lastmod: publishDate.toISOString(),
        changefreq: daysSincePublished < 30 ? 'weekly' : 'monthly',
        priority: Math.min(priority, 0.9).toFixed(1),
        lang: 'fr',
        slug: post.slug
      }
    }),
    ...arPosts.map((post: BlogPost) => {
      const publishDate = new Date(post.publishedAt)
      const daysSincePublished = Math.floor((currentDate.getTime() - publishDate.getTime()) / (1000 * 60 * 60 * 24))
      
      let priority = 0.6
      if (post.featured) priority += 0.2
      if (daysSincePublished < 30) priority += 0.1
      if (daysSincePublished < 7) priority += 0.1

      return {
        url: `${baseUrl}/ar/blog/${post.slug}`,
        lastmod: publishDate.toISOString(),
        changefreq: daysSincePublished < 30 ? 'weekly' : 'monthly',
        priority: Math.min(priority, 0.9).toFixed(1),
        lang: 'ar',
        slug: post.slug
      }
    })
  ]

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${blogUrls.map(post => `  <url>
    <loc>${post.url}</loc>
    <lastmod>${post.lastmod}</lastmod>
    <changefreq>${post.changefreq}</changefreq>
    <priority>${post.priority}</priority>
    <xhtml:link rel="alternate" hreflang="en" href="${baseUrl}/en/blog/${post.slug}" />
    <xhtml:link rel="alternate" hreflang="fr" href="${baseUrl}/fr/blog/${post.slug}" />
    <xhtml:link rel="alternate" hreflang="ar" href="${baseUrl}/ar/blog/${post.slug}" />
  </url>`).join('\n')}
</urlset>`

  return new NextResponse(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=1800, s-maxage=1800', // 30 minutes cache for blog posts
    },
  })
}
