import { NextResponse } from 'next/server'
import { getBaseUrl } from '@/lib/url'

export async function GET() {
  const baseUrl = getBaseUrl()

  // Define images used across the site
  const images = [
    {
      url: `${baseUrl}/en`,
      images: [
        {
          loc: `${baseUrl}/calculator-preview.jpg`,
          caption: 'Morocco Loan Calculator - Calculate Interest and Monthly Payments',
          title: 'RibaCalc - Loan Calculator for Morocco'
        }
      ]
    },
    {
      url: `${baseUrl}/fr`,
      images: [
        {
          loc: `${baseUrl}/calculator-preview.jpg`,
          caption: 'Calculateur de Prêt Maroc - Calculer les Intérêts et Paiements Mensuels',
          title: 'RibaCalc - Calculateur de Prêt pour le Maroc'
        }
      ]
    },
    {
      url: `${baseUrl}/ar`,
      images: [
        {
          loc: `${baseUrl}/calculator-preview.jpg`,
          caption: 'حاسبة القروض المغرب - احسب الفوائد والدفعات الشهرية',
          title: 'ريبا كالك - حاسبة القروض للمغرب'
        }
      ]
    }
  ]

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
${images.map(page => `  <url>
    <loc>${page.url}</loc>
${page.images.map(img => `    <image:image>
      <image:loc>${img.loc}</image:loc>
      <image:caption>${img.caption}</image:caption>
      <image:title>${img.title}</image:title>
    </image:image>`).join('\n')}
  </url>`).join('\n')}
</urlset>`

  return new NextResponse(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400', // 24 hours cache for images
    },
  })
}
