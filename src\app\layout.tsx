import type {Metadata} from 'next';
import './globals.css';

export const metadata: Metadata = {
  title: 'RibaCalc',
  description: 'Morocco Loan Calculator',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet" />
      </head>
      <body className="antialiased font-body">
        {children}
      </body>
    </html>
  );
}
