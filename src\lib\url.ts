/**
 * دالة مساعدة للحصول على URL الأساسي للموقع
 * تستخدم في sitemap و robots.txt و metadata
 */
export function getBaseUrl(): string {
  // أولوية الحصول على URL الأساسي:
  // 1. NEXT_PUBLIC_SITE_URL (للإنتاج)
  // 2. VERCEL_URL (للنشر على Vercel)
  // 3. localhost (للتطوير المحلي)
  
  let baseUrl = process.env.NEXT_PUBLIC_SITE_URL
  
  if (!baseUrl && process.env.VERCEL_URL) {
    baseUrl = `https://${process.env.VERCEL_URL}`
  }
  
  if (!baseUrl) {
    baseUrl = `http://localhost:${process.env.PORT || 3000}`
  }
  
  // إزالة الشرطة المائلة في النهاية إن وجدت
  return baseUrl.replace(/\/$/, '')
}

/**
 * دالة للحصول على URL كامل لصفحة معينة
 */
export function getFullUrl(path: string): string {
  const baseUrl = getBaseUrl()
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}`
}

/**
 * دالة للتحقق من أن URL صحيح
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}
