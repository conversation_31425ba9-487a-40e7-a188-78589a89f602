import { getDictionary } from '@/lib/dictionaries'
import { FAQPage } from '@/components/faq/FAQPage'
import { getFAQItems, getFAQCategories, getFeaturedFAQs } from '@/lib/faq'

type PageProps = {
    params: Promise<{ lang: 'en' | 'fr' | 'ar' }>;
};

export default async function FAQPageRoute({ params }: PageProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);
  const faqs = await getFAQItems(lang);
  const categories = await getFAQCategories(lang);
  const featuredFAQs = await getFeaturedFAQs(lang);
  
  return <FAQPage dict={dict} lang={lang} faqs={faqs} categories={categories} featuredFAQs={featuredFAQs} />
}

export async function generateMetadata({ params }: PageProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);
  
  return {
    title: `${dict.faq.title} | ${dict.seo.title}`,
    description: dict.faq.description,
    alternates: {
      canonical: `/${lang}/faq`,
      languages: {
        'en': '/en/faq',
        'fr': '/fr/faq',
        'ar': '/ar/faq',
      },
    },
    openGraph: {
      title: `${dict.faq.title} | ${dict.seo.title}`,
      description: dict.faq.description,
      type: 'website',
    },
  };
}
