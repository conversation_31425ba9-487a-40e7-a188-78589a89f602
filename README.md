# RibaCalc - Morocco Loan Calculator

A modern, multilingual loan calculator application built with Next.js 15, designed specifically for Morocco and Arab countries. Calculate fixed interest rates, monthly payments, and total repayment amounts with support for multiple currencies and languages.

## 🚀 Features

- **Multi-language Support**: English, French, and Arabic with RTL support
- **Multi-currency Support**: All Arab country currencies plus USD and EUR
- **Automatic Currency Detection**: Based on user's IP geolocation
- **Islamic Banking Awareness**: Educational content about Riba and Islamic finance
- **SEO Optimized**: Complete with sitemaps, structured data, and meta tags
- **Responsive Design**: Works perfectly on all devices
- **Blog & FAQ**: Educational content about loans and financial planning
- **Accessibility**: WCAG compliant with proper ARIA labels
- **Performance**: Optimized for Core Web Vitals

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS with shadcn/ui components
- **Language**: TypeScript
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts for data visualization
- **Deployment**: Vercel/Firebase App Hosting ready
- **Fonts**: Inter (Latin) + Noto Sans Arabic (Arabic)

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Ridanotx/RibaCalc.git
cd RibaCalc
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:9002](http://localhost:9002) in your browser.

## 🔧 Environment Variables

```env
NEXT_PUBLIC_SITE_URL=https://calc.tolabi.net
```

## 📜 Available Scripts

- `npm run dev` - Start development server on port 9002
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run typecheck` - Run TypeScript type checking

## 🌍 Supported Languages

- **English** (`en`): Default language
- **French** (`fr`): For French-speaking users
- **Arabic** (`ar`): RTL support with Arabic fonts and proper text direction

## 💰 Supported Currencies

### Arab Countries
- **MAD** (Moroccan Dirham) - Default
- AED (UAE Dirham)
- SAR (Saudi Riyal)
- EGP (Egyptian Pound)
- KWD (Kuwaiti Dinar)
- QAR (Qatari Riyal)
- BHD (Bahraini Dinar)
- OMR (Omani Rial)
- JOD (Jordanian Dinar)
- TND (Tunisian Dinar)
- DZD (Algerian Dinar)
- LBP (Lebanese Pound)
- IQD (Iraqi Dinar)
- SYP (Syrian Pound)
- YER (Yemeni Rial)
- LYD (Libyan Dinar)
- SDG (Sudanese Pound)

### International
- USD (US Dollar)
- EUR (Euro)

## 🔍 SEO Features

- Automatic sitemap generation for all languages
- Structured data (JSON-LD) for better search visibility
- Multi-language meta tags and hreflang
- Open Graph and Twitter Card tags
- Robots.txt with proper directives
- Optimized for Google Search Console

## 🛡️ Security & Performance

- TypeScript for type safety
- ESLint and Prettier for code quality
- Proper error handling and validation
- Optimized images and fonts
- Minimal bundle size
- Server-side rendering for better SEO

## 🐛 Recent Bug Fixes

### Fixed Issues:
1. **TypeScript Errors**: Fixed language type mismatches in blog and FAQ components
2. **SEO Issues**: Corrected domain references from UAE to Morocco in schema markup
3. **Accessibility**: Added proper `lang` attributes to HTML elements
4. **Performance**: Added viewport meta tag using Next.js 15 best practices
5. **Styling**: Added proper Arabic font support and RTL styles
6. **Build Configuration**: Re-enabled TypeScript and ESLint checks for production builds

### Security Improvements:
- Removed hardcoded placeholder Google verification codes
- Enabled proper TypeScript and ESLint validation
- Added proper error boundaries and validation

## 🌐 Live Demo

Visit the live application at: [https://calc.tolabi.net](https://calc.tolabi.net)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- UI components from [shadcn/ui](https://ui.shadcn.com/)
- Icons from [Lucide](https://lucide.dev/)
- Fonts from [Google Fonts](https://fonts.google.com/)
- Arabic font: [Noto Sans Arabic](https://fonts.google.com/noto/specimen/Noto+Sans+Arabic)
