// Geolocation and currency detection utilities

export interface CountryCurrencyMapping {
  [countryCode: string]: string;
}

// Mapping of country codes to their default currencies
export const countryCurrencyMap: CountryCurrencyMapping = {
  // Arab Countries
  'AE': 'AED', // United Arab Emirates
  'SA': 'SAR', // Saudi Arabia
  'EG': 'EGP', // Egypt
  'KW': 'KWD', // Kuwait
  'QA': 'QAR', // Qatar
  'BH': 'BHD', // Bahrain
  'OM': 'OMR', // Oman
  'JO': 'JOD', // Jordan
  'MA': 'MAD', // Morocco
  'TN': 'TND', // Tunisia
  'DZ': 'DZD', // Algeria
  'LB': 'LBP', // Lebanon
  'IQ': 'IQD', // Iraq
  'SY': 'SYP', // Syria
  'YE': 'YER', // Yemen
  'LY': 'LYD', // Libya
  'SD': 'SDG', // Sudan
  
  // Major International Currencies
  'US': 'USD', // United States
  'CA': 'USD', // Canada (many prefer USD)
  'GB': 'EUR', // United Kingdom (post-Brexit, many use EUR)
  'DE': 'EUR', // Germany
  'FR': 'EUR', // France
  'IT': 'EUR', // Italy
  'ES': 'EUR', // Spain
  'NL': 'EUR', // Netherlands
  'BE': 'EUR', // Belgium
  'AT': 'EUR', // Austria
  'PT': 'EUR', // Portugal
  'IE': 'EUR', // Ireland
  'FI': 'EUR', // Finland
  'GR': 'EUR', // Greece
  'LU': 'EUR', // Luxembourg
  'MT': 'EUR', // Malta
  'CY': 'EUR', // Cyprus
  'SK': 'EUR', // Slovakia
  'SI': 'EUR', // Slovenia
  'EE': 'EUR', // Estonia
  'LV': 'EUR', // Latvia
  'LT': 'EUR', // Lithuania
  'HR': 'EUR', // Croatia
  
  // Other countries that commonly use USD
  'AU': 'USD', // Australia
  'NZ': 'USD', // New Zealand
  'SG': 'USD', // Singapore
  'HK': 'USD', // Hong Kong
  'JP': 'USD', // Japan (international business)
  'KR': 'USD', // South Korea (international business)
  'IN': 'USD', // India (international business)
  'CN': 'USD', // China (international business)
  'BR': 'USD', // Brazil
  'MX': 'USD', // Mexico
  'AR': 'USD', // Argentina
  'CL': 'USD', // Chile
  'CO': 'USD', // Colombia
  'PE': 'USD', // Peru
  'VE': 'USD', // Venezuela
  'ZA': 'USD', // South Africa
  'NG': 'USD', // Nigeria
  'KE': 'USD', // Kenya
  'GH': 'USD', // Ghana
  'ET': 'USD', // Ethiopia
  'TZ': 'USD', // Tanzania
  'UG': 'USD', // Uganda
  'RW': 'USD', // Rwanda
  'TR': 'EUR', // Turkey
  'RU': 'USD', // Russia
  'UA': 'EUR', // Ukraine
  'PL': 'EUR', // Poland
  'CZ': 'EUR', // Czech Republic
  'HU': 'EUR', // Hungary
  'RO': 'EUR', // Romania
  'BG': 'EUR', // Bulgaria
  'RS': 'EUR', // Serbia
  'BA': 'EUR', // Bosnia and Herzegovina
  'MK': 'EUR', // North Macedonia
  'AL': 'EUR', // Albania
  'ME': 'EUR', // Montenegro
  'XK': 'EUR', // Kosovo
  'MD': 'EUR', // Moldova
  'BY': 'EUR', // Belarus
  'GE': 'EUR', // Georgia
  'AM': 'EUR', // Armenia
  'AZ': 'EUR', // Azerbaijan
  'KZ': 'USD', // Kazakhstan
  'UZ': 'USD', // Uzbekistan
  'TM': 'USD', // Turkmenistan
  'KG': 'USD', // Kyrgyzstan
  'TJ': 'USD', // Tajikistan
  'AF': 'USD', // Afghanistan
  'PK': 'USD', // Pakistan
  'BD': 'USD', // Bangladesh
  'LK': 'USD', // Sri Lanka
  'NP': 'USD', // Nepal
  'BT': 'USD', // Bhutan
  'MV': 'USD', // Maldives
  'MM': 'USD', // Myanmar
  'TH': 'USD', // Thailand
  'VN': 'USD', // Vietnam
  'KH': 'USD', // Cambodia
  'LA': 'USD', // Laos
  'MY': 'USD', // Malaysia
  'ID': 'USD', // Indonesia
  'PH': 'USD', // Philippines
  'BN': 'USD', // Brunei
  'TL': 'USD', // East Timor
  'PG': 'USD', // Papua New Guinea
  'FJ': 'USD', // Fiji
  'SB': 'USD', // Solomon Islands
  'VU': 'USD', // Vanuatu
  'NC': 'EUR', // New Caledonia
  'PF': 'EUR', // French Polynesia
  'WF': 'EUR', // Wallis and Futuna
  'CK': 'USD', // Cook Islands
  'NU': 'USD', // Niue
  'TK': 'USD', // Tokelau
  'WS': 'USD', // Samoa
  'TO': 'USD', // Tonga
  'TV': 'USD', // Tuvalu
  'KI': 'USD', // Kiribati
  'NR': 'USD', // Nauru
  'PW': 'USD', // Palau
  'FM': 'USD', // Micronesia
  'MH': 'USD', // Marshall Islands
  'GU': 'USD', // Guam
  'MP': 'USD', // Northern Mariana Islands
  'AS': 'USD', // American Samoa
  'VI': 'USD', // US Virgin Islands
  'PR': 'USD', // Puerto Rico
  'UM': 'USD', // US Minor Outlying Islands
};

// Default currency if country is not found or detection fails
export const DEFAULT_CURRENCY = 'MAD';

/**
 * Get currency based on country code
 */
export function getCurrencyByCountry(countryCode: string): string {
  return countryCurrencyMap[countryCode?.toUpperCase()] || DEFAULT_CURRENCY;
}

/**
 * Detect user's country using IP geolocation
 * This function will be called on the client side
 */
export async function detectUserCountry(): Promise<string | null> {
  try {
    // Try multiple geolocation services for better reliability
    const services = [
      'https://ipapi.co/country_code/',
      'https://api.country.is/',
      'https://ipinfo.io/country',
    ];

    for (const service of services) {
      try {
        const response = await fetch(service, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        });

        if (response.ok) {
          let countryCode: string;
          
          if (service.includes('country.is')) {
            const data = await response.json();
            countryCode = data.country;
          } else {
            countryCode = await response.text();
          }

          // Clean and validate country code
          countryCode = countryCode.trim().toUpperCase();
          if (countryCode && countryCode.length === 2) {
            return countryCode;
          }
        }
      } catch (error) {
        console.warn(`Failed to get country from ${service}:`, error);
        continue;
      }
    }

    return null;
  } catch (error) {
    console.error('Error detecting user country:', error);
    return null;
  }
}

/**
 * Detect user's currency based on their location
 */
export async function detectUserCurrency(): Promise<string> {
  try {
    const countryCode = await detectUserCountry();
    if (countryCode) {
      return getCurrencyByCountry(countryCode);
    }
    return DEFAULT_CURRENCY;
  } catch (error) {
    console.error('Error detecting user currency:', error);
    return DEFAULT_CURRENCY;
  }
}

/**
 * Get user's timezone to help with currency detection
 */
export function getUserTimezone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    return 'UTC';
  }
}

/**
 * Fallback currency detection based on browser language
 */
export function getCurrencyByLanguage(): string {
  try {
    const language = navigator.language || navigator.languages?.[0] || 'en';
    const langCode = language.split('-')[0].toLowerCase();
    
    // Map languages to likely currencies
    const languageCurrencyMap: { [key: string]: string } = {
      'ar': 'MAD', // Arabic - default to Moroccan Dirham
      'en': 'USD', // English - default to US Dollar
      'fr': 'EUR', // French - default to Euro
      'de': 'EUR', // German - Euro
      'es': 'EUR', // Spanish - Euro
      'it': 'EUR', // Italian - Euro
      'pt': 'EUR', // Portuguese - Euro
      'nl': 'EUR', // Dutch - Euro
      'ru': 'USD', // Russian - USD for international
      'zh': 'USD', // Chinese - USD for international
      'ja': 'USD', // Japanese - USD for international
      'ko': 'USD', // Korean - USD for international
      'hi': 'USD', // Hindi - USD for international
    };

    return languageCurrencyMap[langCode] || DEFAULT_CURRENCY;
  } catch (error) {
    return DEFAULT_CURRENCY;
  }
}

/**
 * Smart currency detection combining multiple methods
 */
export async function smartCurrencyDetection(): Promise<string> {
  try {
    // First try IP-based detection
    const ipCurrency = await detectUserCurrency();
    if (ipCurrency !== DEFAULT_CURRENCY) {
      return ipCurrency;
    }

    // Fallback to language-based detection
    const langCurrency = getCurrencyByLanguage();
    return langCurrency;
  } catch (error) {
    console.error('Smart currency detection failed:', error);
    return DEFAULT_CURRENCY;
  }
}
