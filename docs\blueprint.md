# **App Name**: RibaCalc

## Core Features:

- Data Input: Input fields for loan amount, interest rate, and loan duration (in years).
- Interest Calculation: Calculation of the total amount to be paid, incorporating the principal and the fixed interest over the loan term.
- Results Display: Display of the total repayment amount, broken down into principal and interest.
- Results Visualization: A simple and clear results chart.

## Style Guidelines:

- Primary color: Sky blue (#87CEEB) to evoke trust and financial stability.
- Background color: Very light blue (#F0F8FF), close to white, providing a clean backdrop.
- Accent color: Light Green (#90EE90) for positive values and call to action elements.
- Font: 'Inter' (sans-serif) for both headings and body text to ensure readability and a modern feel.
- Use simple, geometric icons to represent different financial concepts. Ensure they are consistent in style and weight.
- Clean and minimalist layout with clear visual hierarchy to ensure ease of use. The input fields should be prominent and the results clearly displayed.
- Subtle animations when the calculation is performed to give a sense of interactivity and feedback to the user.