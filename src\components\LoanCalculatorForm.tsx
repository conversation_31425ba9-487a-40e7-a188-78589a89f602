"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Landmark, Percent, CalendarClock, Calculator, BookOpen, HelpCircle } from "lucide-react";
import Link from "next/link";
import { SchemaMarkup } from "@/components/seo/SchemaMarkup";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { ChartContainer, ChartConfig } from "@/components/ui/chart"
import { <PERSON><PERSON>hart, Pie, Cell, Tooltip } from "recharts"
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency, getDefaultCurrency } from "@/lib/currency";
import CurrencySelector from "@/components/CurrencySelector";

type FormData = {
    loanAmount: number | string;
    interestRate: number | string;
    loanYears: number | string;
};

interface ResultData {
    principal: number;
    totalInterest: number;
    totalPayment: number;
    monthlyPayment: number;
}



const CustomTooltip = ({ active, payload, lang, currency }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border bg-background p-2.5 shadow-sm">
          <div className="grid gap-1.5">
              {payload.map((item: any) => (
                  <div key={item.name} className="flex items-center gap-2">
                      <div className="w-2.5 h-2.5 rounded-full" style={{ backgroundColor: item.payload.fill }} />
                      <div className="flex justify-between flex-1 gap-2">
                          <span className="text-muted-foreground">{item.name}:</span>
                          <span className="font-bold">{formatCurrency(item.value, currency, lang)}</span>
                      </div>
                  </div>
              ))}
          </div>
        </div>
      );
    }
    return null;
  };

export default function LoanCalculatorForm({ dict, lang }: { dict: any; lang: 'en' | 'fr' | 'ar' }) {
    const [result, setResult] = useState<ResultData | null>(null);
    const [isCalculating, setIsCalculating] = useState(false);
    const [selectedCurrency, setSelectedCurrency] = useState<string>(getDefaultCurrency());

    // Create validation schema with translated messages
    const formSchema = z.object({
        loanAmount: z.coerce.number({ invalid_type_error: dict.validation.loanAmountRequired })
            .min(1, { message: dict.validation.loanAmountPositive }),
        interestRate: z.coerce.number({ invalid_type_error: dict.validation.interestRateRequired })
            .min(0, { message: dict.validation.interestRateNonNegative })
            .max(100, { message: dict.validation.interestRateMax }),
        loanYears: z.coerce.number({ invalid_type_error: dict.validation.loanYearsRequired })
            .int()
            .min(1, { message: dict.validation.loanYearsMinimum }),
    });

    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            loanAmount: "",
            interestRate: "",
            loanYears: "",
        },
    });

    const onSubmit = (data: FormData) => {
        setIsCalculating(true);
        setResult(null);
        setTimeout(() => {
            const { loanAmount, interestRate, loanYears } = data;
            const principal = loanAmount;
            const annualRate = interestRate / 100;
            const monthlyRate = annualRate / 12;
            const numberOfPayments = loanYears * 12;

            // Calculate monthly payment using the correct amortization formula
            // PMT = P * [r(1+r)^n] / [(1+r)^n - 1]
            let monthlyPayment: number;
            let totalPayment: number;
            let totalInterest: number;

            if (monthlyRate === 0) {
                // If no interest, just divide principal by number of payments
                monthlyPayment = principal / numberOfPayments;
                totalPayment = principal;
                totalInterest = 0;
            } else {
                // Standard amortization formula
                const numerator = monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments);
                const denominator = Math.pow(1 + monthlyRate, numberOfPayments) - 1;
                monthlyPayment = principal * (numerator / denominator);
                totalPayment = monthlyPayment * numberOfPayments;
                totalInterest = totalPayment - principal;
            }

            setResult({
                principal,
                totalInterest,
                totalPayment,
                monthlyPayment,
            });
            setIsCalculating(false);
        }, 700);
    };

    const chartData = result ? [
            { name: dict.results.pieChartPrincipal, value: result.principal, fill: 'hsl(var(--primary))' },
            { name: dict.results.pieChartTotalInterest, value: result.totalInterest, fill: '#dc2626' },
        ] : [];

    const chartConfig = {
        principal: {
            label: dict.results.pieChartPrincipal,
            color: "hsl(var(--primary))",
        },
        interest: {
            label: dict.results.pieChartTotalInterest,
            color: "#dc2626",
        },
    } satisfies ChartConfig

    return (
        <>
            <SchemaMarkup type="calculator" lang={lang} />
            <div className="flex min-h-screen w-full flex-col items-center justify-center bg-background p-4 sm:p-8 pt-20">
            <div className="w-full max-w-4xl mx-auto container-stable">
                <div className="text-center mb-8">
                    <h1 className="text-4xl sm:text-5xl font-bold text-primary tracking-tight">{dict.header.title}</h1>
                    <p className="text-muted-foreground mt-2 text-lg">{dict.header.subtitle}</p>
                </div>

                <div className="grid md:grid-cols-2 gap-8 grid-stable">
                    <Card className="card-stable">
                        <CardHeader>
                            <CardTitle className="text-2xl">{dict.calculator.title}</CardTitle>
                            <CardDescription>{dict.calculator.description}</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Form {...form}>
                                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                    {/* Currency Selector */}
                                    <CurrencySelector
                                        selectedCurrency={selectedCurrency}
                                        onCurrencyChange={setSelectedCurrency}
                                        lang={lang}
                                        dict={dict}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="loanAmount"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="form-label">{dict.calculator.loanAmount}</FormLabel>
                                                <div className="relative input-wrapper">
                                                    <Landmark className="absolute top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground left-3 rtl:left-auto rtl:right-3 input-icon" />
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            step="100"
                                                            placeholder={dict.calculator.loanAmountPlaceholder}
                                                            className="pl-10 pr-3 rtl:pl-3 rtl:pr-10"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                </div>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="interestRate"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="form-label">{dict.calculator.interestRate}</FormLabel>
                                                <div className="relative input-wrapper">
                                                     <Percent className="absolute top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground left-3 rtl:left-auto rtl:right-3 input-icon" />
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            step="0.1"
                                                            placeholder={dict.calculator.interestRatePlaceholder}
                                                            className="pl-10 pr-3 rtl:pl-3 rtl:pr-10"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                </div>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <FormField
                                        control={form.control}
                                        name="loanYears"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel className="form-label">{dict.calculator.loanTerm}</FormLabel>
                                                 <div className="relative input-wrapper">
                                                    <CalendarClock className="absolute top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground left-3 rtl:left-auto rtl:right-3 input-icon" />
                                                    <FormControl>
                                                        <Input
                                                            type="number"
                                                            placeholder={dict.calculator.loanTermPlaceholder}
                                                            className="pl-10 pr-3 rtl:pl-3 rtl:pr-10"
                                                            {...field}
                                                        />
                                                    </FormControl>
                                                </div>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                    <Button type="submit" className="w-full !mt-8" disabled={isCalculating}>
                                        <Calculator className={`h-4 w-4 ${lang === 'ar' ? 'ml-2' : 'mr-2'}`} />
                                        <span className={lang === 'ar' ? 'arabic-numbers' : ''}>
                                            {isCalculating ? dict.calculator.buttonLoading : dict.calculator.button}
                                        </span>
                                    </Button>
                                </form>
                            </Form>
                        </CardContent>
                    </Card>

                    <div className="card-stable">
                        <Card className="h-full flex flex-col results-stable">
                            <CardHeader>
                                <CardTitle className="text-2xl">{dict.results.title}</CardTitle>
                                <CardDescription>{dict.results.description}</CardDescription>
                            </CardHeader>
                            <CardContent className="flex-grow flex flex-col items-center justify-center">
                                {isCalculating ? (
                                    <div className="flex flex-col items-center justify-center space-y-4 w-full">
                                        <Skeleton className="h-48 w-48 rounded-full" />
                                        <div className="space-y-2 w-full px-4">
                                            <Skeleton className="h-6 w-3/4 mx-auto" />
                                            <Skeleton className="h-6 w-1/2 mx-auto" />
                                        </div>
                                    </div>
                                ) : result ? (
                                   <div className="w-full h-full flex flex-col items-center justify-center animate-in fade-in duration-700">
                                       <div className="w-full h-52">
                                         <ChartContainer config={chartConfig} className="w-full h-full">
                                            <PieChart>
                                                <Tooltip
                                                    cursor={false}
                                                    content={<CustomTooltip lang={lang} currency={selectedCurrency} />}
                                                />
                                                <Pie data={chartData} dataKey="value" nameKey="name" innerRadius={60} outerRadius={80} paddingAngle={5}>
                                                     {chartData.map((entry, index) => (
                                                        <Cell key={`cell-${index}`} fill={entry.fill} />
                                                    ))}
                                                </Pie>
                                            </PieChart>
                                        </ChartContainer>
                                       </div>
                                   </div>
                                ) : (
                                    <div className="text-center text-muted-foreground p-8">
                                        <Calculator className="mx-auto h-16 w-16 mb-4 opacity-50" />
                                        <p className="text-lg font-medium">{dict.results.placeholderTitle}</p>
                                        <p className="text-sm">{dict.results.placeholderDescription}</p>
                                    </div>
                                )}
                            </CardContent>
                            <CardFooter className="flex-col items-start space-y-4 text-lg">
                                {result && !isCalculating && (
                                    <div className="w-full space-y-3 animate-in fade-in duration-700">
                                        <div className="flex justify-between items-center w-full gap-4">
                                            {lang === 'ar' ? (
                                                <>
                                                    <span className="font-semibold text-base arabic-numbers text-left flex-shrink-0">{formatCurrency(result.principal, selectedCurrency, lang)}</span>
                                                    <span className="text-sm text-muted-foreground flex-shrink-0 max-w-[60%]">{dict.results.principal}</span>
                                                </>
                                            ) : (
                                                <>
                                                    <span className="text-sm text-muted-foreground flex-shrink-0 max-w-[60%]">{dict.results.principal}</span>
                                                    <span className="font-semibold text-base arabic-numbers text-right flex-shrink-0">{formatCurrency(result.principal, selectedCurrency, lang)}</span>
                                                </>
                                            )}
                                        </div>
                                        <div className="flex justify-between items-center w-full gap-4">
                                            {lang === 'ar' ? (
                                                <>
                                                    <span className="font-semibold text-base text-red-600 arabic-numbers text-left flex-shrink-0">{formatCurrency(result.totalInterest, selectedCurrency, lang)}</span>
                                                    <span className="text-sm text-red-600 flex-shrink-0 max-w-[60%]">{dict.results.totalInterest}</span>
                                                </>
                                            ) : (
                                                <>
                                                    <span className="text-sm text-red-600 flex-shrink-0 max-w-[60%]">{dict.results.totalInterest}</span>
                                                    <span className="font-semibold text-base text-red-600 arabic-numbers text-right flex-shrink-0">{formatCurrency(result.totalInterest, selectedCurrency, lang)}</span>
                                                </>
                                            )}
                                        </div>
                                        <Separator className="my-2" />
                                        <div className="flex justify-between items-center font-bold w-full gap-4">
                                            {lang === 'ar' ? (
                                                <>
                                                    <span className="text-lg arabic-numbers text-left flex-shrink-0">{formatCurrency(result.totalPayment, selectedCurrency, lang)}</span>
                                                    <span className="text-primary flex-shrink-0 max-w-[60%]">{dict.results.totalRepayment}</span>
                                                </>
                                            ) : (
                                                <>
                                                    <span className="text-primary flex-shrink-0 max-w-[60%]">{dict.results.totalRepayment}</span>
                                                    <span className="text-lg arabic-numbers text-right flex-shrink-0">{formatCurrency(result.totalPayment, selectedCurrency, lang)}</span>
                                                </>
                                            )}
                                        </div>
                                         <div className="flex justify-between items-center text-base pt-1 text-muted-foreground w-full gap-4">
                                            {lang === 'ar' ? (
                                                <>
                                                    <span className="font-semibold arabic-numbers text-left flex-shrink-0">{formatCurrency(result.monthlyPayment, selectedCurrency, lang)}</span>
                                                    <span className="flex-shrink-0 max-w-[60%]">{dict.results.monthlyPayment}</span>
                                                </>
                                            ) : (
                                                <>
                                                    <span className="flex-shrink-0 max-w-[60%]">{dict.results.monthlyPayment}</span>
                                                    <span className="font-semibold arabic-numbers text-right flex-shrink-0">{formatCurrency(result.monthlyPayment, selectedCurrency, lang)}</span>
                                                </>
                                            )}
                                        </div>

                                        {/* تحذيرات التكلفة الحقيقية */}
                                        {result.totalInterest > 0 && (
                                            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                                                <div className="flex items-start gap-3">
                                                    <div className="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                                        <span className="text-red-600 text-sm font-bold">!</span>
                                                    </div>
                                                    <div className="flex-1">
                                                        <h4 className="text-red-800 font-bold text-sm mb-2">
                                                            {lang === 'ar' ? 'تحذير: التكلفة الحقيقية للربا' :
                                                             lang === 'fr' ? 'Attention: Coût réel de l\'intérêt' :
                                                             'Warning: Real Cost of Interest'}
                                                        </h4>
                                                        <div className="space-y-2 text-xs text-red-700">
                                                            <p>
                                                                {lang === 'ar' ?
                                                                    `ستدفع ${formatCurrency(result.totalInterest, selectedCurrency, lang)} إضافية كفوائد ربوية محرمة!` :
                                                                lang === 'fr' ?
                                                                    `Vous paierez ${formatCurrency(result.totalInterest, selectedCurrency, lang)} supplémentaires en intérêts!` :
                                                                    `You will pay ${formatCurrency(result.totalInterest, selectedCurrency, lang)} extra in interest!`
                                                                }
                                                            </p>
                                                            <p className="font-semibold">
                                                                {lang === 'ar' ?
                                                                    `نسبة الزيادة: ${((result.totalInterest / result.principal) * 100).toFixed(1)}% من المبلغ الأصلي` :
                                                                lang === 'fr' ?
                                                                    `Augmentation: ${((result.totalInterest / result.principal) * 100).toFixed(1)}% du montant initial` :
                                                                    `Increase: ${((result.totalInterest / result.principal) * 100).toFixed(1)}% of original amount`
                                                                }
                                                            </p>
                                                            {lang === 'ar' && (
                                                                <p className="text-red-800 font-bold">
                                                                    "الَّذِينَ يَأْكُلُونَ الرِّبَا لَا يَقُومُونَ إِلَّا كَمَا يَقُومُ الَّذِي يَتَخَبَّطُهُ الشَّيْطَانُ مِنَ الْمَسِّ"
                                                                </p>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {/* نصائح البدائل الإسلامية */}
                                        {result.totalInterest > 0 && (
                                            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                                                <div className="flex items-start gap-3">
                                                    <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                                        <span className="text-green-600 text-sm">✓</span>
                                                    </div>
                                                    <div className="flex-1">
                                                        <h4 className="text-green-800 font-bold text-sm mb-2">
                                                            {lang === 'ar' ? 'البدائل الإسلامية الآمنة' :
                                                             lang === 'fr' ? 'Alternatives islamiques sûres' :
                                                             'Safe Islamic Alternatives'}
                                                        </h4>
                                                        <div className="space-y-1 text-xs text-green-700">
                                                            <p>• {lang === 'ar' ? 'المرابحة: شراء البنك للسلعة وبيعها بربح معلوم' :
                                                                   lang === 'fr' ? 'Murabaha: Achat par la banque et revente avec profit connu' :
                                                                   'Murabaha: Bank purchases and resells with known profit'}</p>
                                                            <p>• {lang === 'ar' ? 'الإجارة: تأجير مع وعد بالتمليك' :
                                                                   lang === 'fr' ? 'Ijara: Location avec promesse de propriété' :
                                                                   'Ijara: Lease with promise of ownership'}</p>
                                                            <p>• {lang === 'ar' ? 'المشاركة: شراكة عادلة في الاستثمار' :
                                                                   lang === 'fr' ? 'Musharaka: Partenariat équitable d\'investissement' :
                                                                   'Musharaka: Fair investment partnership'}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </CardFooter>
                        </Card>
                    </div>
                </div>

                {/* Featured Blog Posts Section */}
                <div className="mt-16 text-center">
                    <h2 className="text-2xl font-bold text-red-600 mb-4">
                        {lang === 'ar' ? 'تحذير: اقرأ قبل أن تقترض!' :
                         lang === 'fr' ? 'Attention: Lisez avant d\'emprunter!' :
                         'Warning: Read Before You Borrow!'}
                    </h2>
                    <p className="text-muted-foreground mb-8">
                        {lang === 'ar' ? 'مقالات مهمة تكشف لك الحقيقة الكاملة عن مخاطر الربا والبدائل الآمنة' :
                         lang === 'fr' ? 'Articles importants révélant la vérité sur les risques des intérêts et les alternatives sûres' :
                         'Important articles revealing the truth about interest risks and safe alternatives'}
                    </p>
                    <div className="grid md:grid-cols-3 gap-6 mb-8">
                        {/* مقال مخاطر الربا */}
                        <Card className={`${lang === 'ar' ? 'text-right' : 'text-left'} hover:shadow-lg transition-shadow border-red-200 bg-red-50`}>
                            <CardHeader>
                                <CardTitle className="text-lg text-red-800">
                                    {lang === 'ar' ? 'مخاطر الربا: لماذا حرمه الإسلام؟' :
                                     lang === 'fr' ? 'Dangers de l\'intérêt: Pourquoi l\'Islam l\'interdit?' :
                                     'Dangers of Interest: Why Islam Forbids It?'}
                                </CardTitle>
                                <CardDescription className="text-red-600">
                                    {lang === 'ar' ? 'تحليل شامل لمخاطر الربا الدينية والاقتصادية والاجتماعية مع أمثلة حقيقية' :
                                     lang === 'fr' ? 'Analyse complète des risques religieux, économiques et sociaux de l\'intérêt' :
                                     'Comprehensive analysis of religious, economic and social risks of interest'}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Link
                                    href={`/${lang}/blog/dangers-of-riba-islamic-perspective`}
                                    className="inline-flex items-center text-red-600 hover:text-red-800 font-semibold"
                                >
                                    {lang === 'ar' ? (
                                        <>
                                            <BookOpen className="h-4 w-4 ml-2" />
                                            اقرأ التحذير
                                        </>
                                    ) : (
                                        <>
                                            Read Warning
                                            <BookOpen className="h-4 w-4 ml-2" />
                                        </>
                                    )}
                                </Link>
                            </CardContent>
                        </Card>

                        {/* مقال التكلفة الحقيقية */}
                        <Card className={`${lang === 'ar' ? 'text-right' : 'text-left'} hover:shadow-lg transition-shadow border-orange-200 bg-orange-50`}>
                            <CardHeader>
                                <CardTitle className="text-lg text-orange-800">
                                    {lang === 'ar' ? 'احسب قبل أن تندم: التكلفة الحقيقية' :
                                     lang === 'fr' ? 'Calculez avant de regretter: Le coût réel' :
                                     'Calculate Before You Regret: Real Cost'}
                                </CardTitle>
                                <CardDescription className="text-orange-600">
                                    {lang === 'ar' ? 'اكتشف كم ستدفع فعلياً وكيف تتجنب الفخاخ المالية المدمرة' :
                                     lang === 'fr' ? 'Découvrez combien vous paierez réellement et comment éviter les pièges financiers' :
                                     'Discover how much you\'ll actually pay and avoid financial traps'}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Link
                                    href={`/${lang}/blog/real-cost-of-loans-calculator`}
                                    className="inline-flex items-center text-orange-600 hover:text-orange-800 font-semibold"
                                >
                                    {lang === 'ar' ? (
                                        <>
                                            <BookOpen className="h-4 w-4 ml-2" />
                                            احسب الحقيقة
                                        </>
                                    ) : (
                                        <>
                                            Calculate Truth
                                            <BookOpen className="h-4 w-4 ml-2" />
                                        </>
                                    )}
                                </Link>
                            </CardContent>
                        </Card>

                        {/* مقال البدائل الإسلامية */}
                        <Card className={`${lang === 'ar' ? 'text-right' : 'text-left'} hover:shadow-lg transition-shadow border-green-200 bg-green-50`}>
                            <CardHeader>
                                <CardTitle className="text-lg text-green-800">
                                    {lang === 'ar' ? 'البدائل الإسلامية الآمنة' :
                                     lang === 'fr' ? 'Alternatives islamiques sûres' :
                                     'Safe Islamic Alternatives'}
                                </CardTitle>
                                <CardDescription className="text-green-600">
                                    {lang === 'ar' ? 'تعرف على المرابحة والإجارة والمشاركة - الحلول الحلال للتمويل' :
                                     lang === 'fr' ? 'Découvrez Murabaha, Ijara et Musharaka - Solutions halal de financement' :
                                     'Learn about Murabaha, Ijara and Musharaka - Halal financing solutions'}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Link
                                    href={`/${lang}/blog/islamic-alternatives-to-riba-loans`}
                                    className="inline-flex items-center text-green-600 hover:text-green-800 font-semibold"
                                >
                                    {lang === 'ar' ? (
                                        <>
                                            <BookOpen className="h-4 w-4 ml-2" />
                                            اكتشف البدائل
                                        </>
                                    ) : (
                                        <>
                                            Discover Alternatives
                                            <BookOpen className="h-4 w-4 ml-2" />
                                        </>
                                    )}
                                </Link>
                            </CardContent>
                        </Card>
                    </div>

                    {/* تحذير نهائي */}
                    <div className="mt-8 p-6 bg-red-100 border-2 border-red-300 rounded-lg">
                        <div className="text-center">
                            <h3 className="text-xl font-bold text-red-800 mb-3">
                                {lang === 'ar' ? '⚠️ تذكر دائماً ⚠️' :
                                 lang === 'fr' ? '⚠️ Rappelez-vous toujours ⚠️' :
                                 '⚠️ Always Remember ⚠️'}
                            </h3>
                            <p className="text-red-700 font-semibold text-lg">
                                {lang === 'ar' ? 'كل درهم فائدة هو حرام شرعاً وخسارة مالياً' :
                                 lang === 'fr' ? 'Chaque dirham d\'intérêt est interdit religieusement et une perte financière' :
                                 'Every dirham of interest is religiously forbidden and financially harmful'}
                            </p>
                            {lang === 'ar' && (
                                <p className="text-red-800 font-bold mt-2 text-base">
                                    "يَمْحَقُ اللَّهُ الرِّبَا وَيُرْبِي الصَّدَقَاتِ"
                                </p>
                            )}
                        </div>
                    </div>

                    <div className="flex gap-4 justify-center mt-8">
                        <Button variant="outline" asChild>
                            <Link href={`/${lang}/blog`}>
                                {lang === 'ar' ? (
                                    <>
                                        <BookOpen className="h-4 w-4 ml-2" />
                                        اقرأ المزيد من التحذيرات
                                    </>
                                ) : (
                                    <>
                                        Read More Warnings
                                        <BookOpen className="h-4 w-4 ml-2" />
                                    </>
                                )}
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href={`/${lang}/faq`}>
                                {lang === 'ar' ? (
                                    <>
                                        <HelpCircle className="h-4 w-4 ml-2" />
                                        {dict.navigation?.faq || 'Common Questions'}
                                    </>
                                ) : (
                                    <>
                                        {dict.navigation?.faq || 'Common Questions'}
                                        <HelpCircle className="h-4 w-4 ml-2" />
                                    </>
                                )}
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Quick FAQ Section */}
                <div className="mt-16 text-center">
                    <h2 className="text-2xl font-bold text-primary mb-4">{dict.quickAnswers?.title || 'Quick Answers'}</h2>
                    <p className="text-muted-foreground mb-8">
                        {dict.quickAnswers?.description || 'Get instant answers to common loan questions'}
                    </p>
                    <div className="grid md:grid-cols-2 gap-6 mb-8">
                        <Card className={`${lang === 'ar' ? 'text-right' : 'text-left'} hover:shadow-lg transition-shadow`}>
                            <CardHeader>
                                <CardTitle className="text-lg">{dict.quickAnswers?.whatIsRiba || 'What is Riba?'}</CardTitle>
                                <CardDescription>
                                    {dict.quickAnswers?.whatIsRibaDescription || 'Understanding the Islamic concept of interest and its implications for loans.'}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Link
                                    href={`/${lang}/faq#what-is-riba`}
                                    className="inline-flex items-center text-primary hover:text-primary/80"
                                >
                                    {dict.quickAnswers?.readAnswer || 'Read Answer'}
                                    <HelpCircle className={`h-4 w-4 ${lang === 'ar' ? 'mr-2' : 'ml-2'}`} />
                                </Link>
                            </CardContent>
                        </Card>
                        <Card className={`${lang === 'ar' ? 'text-right' : 'text-left'} hover:shadow-lg transition-shadow`}>
                            <CardHeader>
                                <CardTitle className="text-lg">{dict.quickAnswers?.howFixedInterestCalculated || 'How is Fixed Interest Calculated?'}</CardTitle>
                                <CardDescription>
                                    {dict.quickAnswers?.howFixedInterestCalculatedDescription || 'Learn the formula and method for calculating fixed interest on loans.'}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Link
                                    href={`/${lang}/faq#how-fixed-interest-calculated`}
                                    className="inline-flex items-center text-primary hover:text-primary/80"
                                >
                                    {dict.quickAnswers?.readAnswer || 'Read Answer'}
                                    <HelpCircle className={`h-4 w-4 ${lang === 'ar' ? 'mr-2' : 'ml-2'}`} />
                                </Link>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </div>
        </>
    );
}
