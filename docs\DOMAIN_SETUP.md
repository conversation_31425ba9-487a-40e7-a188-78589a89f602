# إعداد النطاق للموقع

## نظرة عامة
يستخدم الموقع نظام ذكي لتحديد URL الأساسي للموقع في ملفات sitemap و robots.txt و metadata.

## أولوية تحديد URL الأساسي

1. **NEXT_PUBLIC_SITE_URL** (الأولوية الأولى)
   - يُستخدم في الإنتاج
   - يجب تعيينه في ملف `.env` للإنتاج

2. **VERCEL_URL** (الأولوية الثانية)
   - يُستخدم تلقائياً عند النشر على Vercel
   - لا يحتاج إعداد يدوي

3. **localhost** (الأولوية الثالثة)
   - يُستخدم في التطوير المحلي
   - القيمة الافتراضية: `http://localhost:3000`

## إعداد النطاق للإنتاج

### 1. تحديث ملف `.env`
```env
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
```

### 2. تحديث ملف `src/app/[lang]/layout.tsx`
```typescript
metadataBase: new URL('https://yourdomain.com'),
```

### 3. تحديث OpenGraph URLs
```typescript
openGraph: {
  url: `https://yourdomain.com/${lang}`,
  siteName: 'YourSiteName',
}
```

## إعداد النطاق للتطوير المحلي

### ملف `.env.local` (لا يُرفع إلى Git)
```env
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NODE_ENV=development
```

## التحقق من الإعداد

### 1. فحص sitemap
```bash
curl http://localhost:3000/sitemap.xml
```

### 2. فحص robots.txt
```bash
curl http://localhost:3000/robots.txt
```

### 3. فحص sitemap index
```bash
curl http://localhost:3000/sitemap-index.xml
```

## ملاحظات مهمة

- **لا تضع نطاقات محددة في الكود** - استخدم متغيرات البيئة
- **ملف `.env.local` للتطوير فقط** - لن يُرفع إلى Git
- **ملف `.env` للإنتاج** - يحتوي على الإعدادات الأساسية
- **التحديث التلقائي** - sitemap يتحدث تلقائياً عند تغيير النطاق

## استكشاف الأخطاء

### المشكلة: sitemap يظهر localhost في الإنتاج
**الحل:** تأكد من تعيين `NEXT_PUBLIC_SITE_URL` في متغيرات البيئة

### المشكلة: النطاق خاطئ في metadata
**الحل:** تحديث `metadataBase` في `layout.tsx`

### المشكلة: robots.txt يشير إلى نطاق خاطئ
**الحل:** التأكد من إعداد متغيرات البيئة بشكل صحيح
