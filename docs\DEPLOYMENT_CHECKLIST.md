# Deployment Checklist for RibaCalc Sitemaps

## Pre-Deployment

### Environment Variables
- [ ] Set `NEXT_PUBLIC_SITE_URL` to your production domain
- [ ] Verify `VERCEL_URL` is automatically set (for Vercel deployments)
- [ ] Test sitemap URLs in staging environment

### Code Verification
- [ ] All sitemap files are present in `src/app/`
- [ ] Middleware excludes sitemap files from language redirection
- [ ] Blog posts are being generated correctly
- [ ] All language variants (en, fr, ar) are included

## Post-Deployment

### Sitemap Verification
- [ ] Test main sitemap: `https://yourdomain.com/sitemap.xml`
- [ ] Test sitemap index: `https://yourdomain.com/sitemap-index.xml`
- [ ] Test pages sitemap: `https://yourdomain.com/sitemap-pages.xml`
- [ ] Test blog sitemap: `https://yourdomain.com/sitemap-blog.xml`
- [ ] Test images sitemap: `https://yourdomain.com/sitemap-images.xml`
- [ ] Test robots.txt: `https://yourdomain.com/robots.txt`

### Search Engine Submission
- [ ] Submit main sitemap to Google Search Console
- [ ] Submit sitemap to Bing Webmaster Tools
- [ ] Verify sitemap is accessible and valid
- [ ] Check for any crawl errors

### SEO Verification
- [ ] Verify all pages are included in sitemap
- [ ] Check priority values are appropriate
- [ ] Confirm change frequencies are realistic
- [ ] Validate hreflang annotations for multilingual content

### Performance Monitoring
- [ ] Monitor sitemap access in server logs
- [ ] Check sitemap cache headers are working
- [ ] Verify sitemap generation time is acceptable
- [ ] Monitor search engine crawl patterns

## Ongoing Maintenance

### Weekly Tasks
- [ ] Check for new blog posts in sitemap
- [ ] Verify sitemap accessibility
- [ ] Monitor search console for sitemap errors

### Monthly Tasks
- [ ] Review sitemap performance metrics
- [ ] Update priority values if needed
- [ ] Check for broken links in sitemap
- [ ] Validate sitemap against current site structure

### Quarterly Tasks
- [ ] Full sitemap audit
- [ ] Review and update change frequencies
- [ ] Optimize sitemap structure if needed
- [ ] Update documentation if changes made

## Emergency Procedures

### Sitemap Not Accessible
1. Check middleware configuration
2. Verify environment variables
3. Test in development environment
4. Check server logs for errors

### Missing Pages in Sitemap
1. Verify page routes exist
2. Check static pages array
3. Validate blog post generation
4. Test sitemap generation locally

### Search Engine Issues
1. Resubmit sitemap to search consoles
2. Check for validation errors
3. Verify robots.txt is correct
4. Monitor crawl errors and fix issues

## Contact Information

For sitemap-related issues:
- Check documentation in `/docs/SITEMAP.md`
- Review implementation in `/src/app/sitemap*.ts`
- Test locally using development server
